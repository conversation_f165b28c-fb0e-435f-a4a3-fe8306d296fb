﻿using System.Globalization;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using DataVenia.Modules.Lawsuits.Domain.LawsuitSteps;
using Result = FluentResults.Result;

namespace DataVenia.Modules.Lawsuits.Domain.Lawsuits;

public sealed class Lawsuit : Entity
{
    private readonly List<LawsuitData> _lawsuitDatas = [];
    private readonly List<MonitoringHistoryEntry> _monitoringHistory = [];
    private readonly List<LawsuitStep> _lawsuitSteps = [];
    private readonly List<DataDivergence> _dataDivergences = [];
    private Lawsuit() { }

    public Guid Id { get; private set; }
    public Guid OfficeId { get; private set; }
    public string Cnj { get; private set; }
    public string? LawsuitTypeId {  get; private set; } // Natureza
    public string? ClassId { get; private set; }
    public DateTime? DistributedAt { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public bool MonitoringEnabled { get; private set; }
    public bool IsFirstTimeSyncCompleted { get; private set; }
    public IReadOnlyCollection<MonitoringHistoryEntry> MonitoringHistory => _monitoringHistory.AsReadOnly();
    public IReadOnlyCollection<LawsuitData> LawsuitDatas => _lawsuitDatas.AsReadOnly();
    public ICollection<LawsuitStep> LawsuitSteps => _lawsuitSteps.AsReadOnly();
    public IReadOnlyCollection<DataDivergence> DataDivergences => _dataDivergences.AsReadOnly();

    public static Lawsuit Create(
                                string cnj,
                                Guid officeId,
                                string lawsuitTypeId,
                                string classId,
                                DateTime distributedAt
                                )
    {
        var lawsuit = new Lawsuit
        {
            Id = Guid.CreateVersion7(),
            OfficeId = officeId,
            Cnj = cnj,
            LawsuitTypeId = lawsuitTypeId,
            ClassId = classId,
            CreatedAt = DateTime.UtcNow,
            DistributedAt = distributedAt,
            MonitoringEnabled = false,
            IsFirstTimeSyncCompleted = false,
        };

        return lawsuit;
    }

    public static Lawsuit CreateByCnj(string cnj, Guid officeId, string? lawsuitTypeId, Guid userId)
    {
        var lawsuit = new Lawsuit
        {
            Id = Guid.CreateVersion7(),
            OfficeId = officeId,
            Cnj = cnj,
            LawsuitTypeId = lawsuitTypeId,
            CreatedAt = DateTime.UtcNow,
            MonitoringEnabled = false,
            IsFirstTimeSyncCompleted = false
        };

        return lawsuit;
    }

    /// <summary>
    /// Activates monitoring. Validates that any previous monitoring period is closed.
    /// </summary>
    public Result ActivateMonitoring(Guid userId)
    {
        // If there is an existing monitoring history and its last entry is not closed, return an error.
        if (_monitoringHistory.Any() && _monitoringHistory[^1].StoppedAt == null)
            return Result.Fail(new FluentResults.Error("Monitoring.AlreadyActive").WithMetadata("StatusCode", 400));

        MonitoringEnabled = true;
        _monitoringHistory.Add(new MonitoringHistoryEntry(DateTime.UtcNow, userId));
        return Result.Ok();
    }

    /// <summary>
    /// Deactivates monitoring. Validates that the last monitoring period is still active.
    /// </summary>
    public Result DeactivateMonitoring(Guid userId)
    {
        // If there is no monitoring history or the last entry is already closed, return an error.
        if (!_monitoringHistory.Any() || _monitoringHistory[^1].StoppedAt != null)
            return Result.Fail(new FluentResults.Error("Monitoring.NotActive").WithMetadata("StatusCode", 400));

        MonitoringEnabled = false;
        _monitoringHistory[^1].SetStoppedAt(DateTime.UtcNow, userId);
        return Result.Ok();
    }

    public void AddOrUpdateDataDivergence(DataDivergence divergentFields)
    {
        if (divergentFields.Fields.Count == 0)
            return;

        // Find existing divergence for this instance
        var existingDivergence = _dataDivergences
            .Where(d => d.InstanceId == divergentFields.InstanceId && !d.AnalyzedAt.HasValue)
            .OrderByDescending(d => d.CreatedAt)
            .FirstOrDefault();

        if (existingDivergence != null)
        {
            var divergencesFromLastSeenTime = 0;

            foreach (var (fieldName, (_, __)) in divergentFields.Fields)
            {
                if (existingDivergence.HasSameEventValue(fieldName, divergentFields.Fields[fieldName].EventValue))
                {
                    divergencesFromLastSeenTime++;
                }
            }

            if (divergencesFromLastSeenTime > 0)
            {
                _dataDivergences.Add(DataDivergence.Create(divergentFields.InstanceId, divergentFields.Fields.ToDictionary(f => f.Key, f => f.Value)));
            }
        }
        else
        {
            _dataDivergences.Add(DataDivergence.Create(divergentFields.InstanceId, divergentFields.Fields.ToDictionary(f => f.Key, f => f.Value)));
        }
    }

    public void MarkDataDivergenceAsAnalyzed(Guid divergenceId, string analyzedBy, bool wasAccepted)
    {
        var divergence = _dataDivergences.FirstOrDefault(d => d.Id == divergenceId);
        if (divergence != null)
            divergence.MarkAsAnalyzed(analyzedBy, wasAccepted);
    }

    public void CheckAndUpdateCoverData(string instanceId, string? lawsuitTypeId, string? classId, DateTime? distributedAt,
        string? judgingOrganId = null, decimal? causeValue = null, List<string>? topicIds = null)
    {
        var divergentFields = new Dictionary<string, (string current, string eventValue)>();
        
        // Check LawsuitTypeId
        if (!string.Equals(LawsuitTypeId, lawsuitTypeId))
        {
            if (string.IsNullOrEmpty(LawsuitTypeId))
                LawsuitTypeId = lawsuitTypeId;
            else
                divergentFields["LawsuitTypeId"] = (LawsuitTypeId ?? string.Empty, lawsuitTypeId ?? string.Empty);
        }

        // Check ClassId
        if (!string.Equals(ClassId, classId))
        {
            if (string.IsNullOrEmpty(ClassId))
                ClassId = classId;
            else
                divergentFields["ClassId"] = (ClassId ?? string.Empty, classId ?? string.Empty);
        }

        // Check DistributedAt
        // Exclude both default(DateTime) and DateTime.MinValue (01/01/0001 00:00:00)
        bool isValidDistributedAt = distributedAt.HasValue &&
                                   distributedAt != DateTime.MinValue &&
                                   distributedAt.Value.Year > 1;

        if (isValidDistributedAt && (!DistributedAt.HasValue || DistributedAt != distributedAt))
        {
            bool isCurrentValueEmpty = !DistributedAt.HasValue ||
                                      DistributedAt == null ||
                                      DistributedAt == DateTime.MinValue ||
                                      DistributedAt.Value.Year <= 1;

            if (isCurrentValueEmpty)
                // If our value is empty or invalid, update it
                DistributedAt = distributedAt;
            else
                // If our value is different, add to divergent fields
                divergentFields["DistributedAt"] = (DistributedAt?.ToString("yyyy-MM-dd") ?? string.Empty, distributedAt!.Value.ToString("yyyy-MM-dd"));
        }

        // Get the latest lawsuit data for this instance
        var latestLawsuitData = _lawsuitDatas.OrderByDescending(d => d.CreatedAt).FirstOrDefault(x => x.LegalInstanceId == instanceId);

        // Check JudgingOrganId
        if (!string.IsNullOrEmpty(judgingOrganId) && latestLawsuitData != null &&
            !string.IsNullOrEmpty(latestLawsuitData.JudgingOrganId) &&
            !string.Equals(latestLawsuitData.JudgingOrganId, judgingOrganId))
        {
            divergentFields["JudgingOrganId"] = (latestLawsuitData.JudgingOrganId, judgingOrganId);
        }

        // Check CauseValue
        if (causeValue.HasValue && causeValue.Value > 0 && latestLawsuitData != null &&
            latestLawsuitData.CauseValue.HasValue && latestLawsuitData.CauseValue.Value > 0 &&
            latestLawsuitData.CauseValue != causeValue)
        {
            divergentFields["CauseValue"] = (latestLawsuitData.CauseValue?.ToString() ?? string.Empty, causeValue.Value.ToString(CultureInfo.InvariantCulture));
        }

        // Check TopicIds
        if (topicIds != null && topicIds.Any() && latestLawsuitData != null && latestLawsuitData.TopicIds.Any())
        {
            var numericTopicIds = new List<int>();

            // Try to convert string topic IDs to integers
            foreach (var topicIdStr in topicIds)
            {
                if (int.TryParse(topicIdStr, out int topicId))
                {
                    numericTopicIds.Add(topicId);
                }
            }

            if (numericTopicIds.Any())
            {
                // Check for differences
                var currentTopicIds = latestLawsuitData.TopicIds.ToHashSet();
                var eventTopicIds = numericTopicIds.ToHashSet();

                // Find topics in the event that are not in our current data
                var missingTopics = eventTopicIds.Except(currentTopicIds).ToList();

                if (missingTopics.Any())
                {
                    // Add to divergent fields
                    var currentTopicsStr = string.Join(", ", latestLawsuitData.TopicIds);
                    var eventTopicsStr = string.Join(", ", numericTopicIds);
                    divergentFields["TopicIds"] = (currentTopicsStr, eventTopicsStr);
                }
            }
        }

        // If we have any divergent fields, add them all to a single divergence
        if (divergentFields.Any())
        {
            var parsedDivergentFields = DataDivergence.Create(instanceId, divergentFields.ToDictionary(f => f.Key, f => new DivergentField(f.Value.current, f.Value.eventValue)));
            
            AddOrUpdateDataDivergence(parsedDivergentFields);
        }
    }

    public void SetFirstTimeSyncCompleted()
    {
        IsFirstTimeSyncCompleted = true;
    }
}
